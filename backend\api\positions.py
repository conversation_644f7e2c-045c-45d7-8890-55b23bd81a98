from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
import pandas as pd

from database import get_db
from models import Position
from services.document_service import DocumentService

router = APIRouter()

@router.get("/", response_model=List[dict])
async def get_positions(db: Session = Depends(get_db)):
    """获取所有岗位信息"""
    positions = db.query(Position).all()
    return [
        {
            "id": pos.id,
            "category": pos.category,
            "name": pos.name,
            "created_at": pos.created_at
        }
        for pos in positions
    ]

@router.post("/")
async def create_position(
    category: str,
    name: str,
    db: Session = Depends(get_db)
):
    """创建新岗位"""
    # 检查是否已存在
    existing = db.query(Position).filter(
        Position.category == category,
        Position.name == name
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="岗位已存在")
    
    position = Position(category=category, name=name)
    db.add(position)
    db.commit()
    db.refresh(position)
    
    return {
        "id": position.id,
        "category": position.category,
        "name": position.name,
        "created_at": position.created_at
    }

@router.put("/{position_id}")
async def update_position(
    position_id: int,
    category: str,
    name: str,
    db: Session = Depends(get_db)
):
    """更新岗位信息"""
    position = db.query(Position).filter(Position.id == position_id).first()
    if not position:
        raise HTTPException(status_code=404, detail="岗位不存在")
    
    position.category = category
    position.name = name
    db.commit()
    db.refresh(position)
    
    return {
        "id": position.id,
        "category": position.category,
        "name": position.name,
        "updated_at": position.updated_at
    }

@router.delete("/{position_id}")
async def delete_position(position_id: int, db: Session = Depends(get_db)):
    """删除岗位"""
    position = db.query(Position).filter(Position.id == position_id).first()
    if not position:
        raise HTTPException(status_code=404, detail="岗位不存在")
    
    db.delete(position)
    db.commit()
    
    return {"message": "岗位删除成功"}

@router.post("/import-excel")
async def import_positions_from_excel(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """从Excel文件导入岗位信息"""
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="请上传Excel文件")
    
    try:
        # 保存上传的文件
        file_path = await DocumentService.save_upload_file(file)
        
        # 读取Excel内容
        positions_data = await DocumentService.read_excel_positions(file_path)
        
        # 批量导入
        imported_count = 0
        for pos_data in positions_data:
            # 检查是否已存在
            existing = db.query(Position).filter(
                Position.category == pos_data["category"],
                Position.name == pos_data["name"]
            ).first()
            
            if not existing:
                position = Position(
                    category=pos_data["category"],
                    name=pos_data["name"]
                )
                db.add(position)
                imported_count += 1
        
        db.commit()
        
        return {
            "message": f"成功导入 {imported_count} 个岗位",
            "total_processed": len(positions_data),
            "imported": imported_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")

@router.get("/categories")
async def get_categories(db: Session = Depends(get_db)):
    """获取所有岗位大类"""
    categories = db.query(Position.category).distinct().all()
    return [cat[0] for cat in categories]

@router.get("/by-category/{category}")
async def get_positions_by_category(category: str, db: Session = Depends(get_db)):
    """根据大类获取岗位"""
    positions = db.query(Position).filter(Position.category == category).all()
    return [
        {
            "id": pos.id,
            "name": pos.name
        }
        for pos in positions
    ]
