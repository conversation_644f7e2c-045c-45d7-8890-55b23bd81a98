from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class Position(Base):
    """岗位分类表"""
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, index=True)
    category = Column(String(100), nullable=False, comment="岗位大类")
    name = Column(String(100), nullable=False, comment="岗位名称")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    jds = relationship("JobDescription", back_populates="position")

class JobDescription(Base):
    """JD职位描述表"""
    __tablename__ = "job_descriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    source_type = Column(String(20), nullable=False, comment="数据来源：manual/upload")
    position_id = Column(Integer, ForeignKey("positions.id"), nullable=False)
    company = Column(String(200), comment="隶属公司")
    department = Column(String(100), comment="部门")
    location = Column(String(200), comment="工作地点")
    
    # AI提取的字段
    require_gender = Column(String(20), comment="性别要求")
    require_age = Column(String(50), comment="年龄要求")
    require_work_year = Column(String(100), comment="工作年限要求")
    require_edu_level = Column(String(50), comment="学历要求")
    require_school = Column(String(200), comment="学校要求")
    require_major = Column(String(500), comment="专业要求")
    
    # 原始文档信息
    original_filename = Column(String(500), comment="原始文件名")
    file_path = Column(String(1000), comment="文件路径")
    raw_content = Column(Text, comment="原始内容")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    position = relationship("Position", back_populates="jds")
    resumes = relationship("Resume", back_populates="job_description")

class Resume(Base):
    """简历表"""
    __tablename__ = "resumes"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="姓名")
    gender = Column(String(10), comment="性别")
    age = Column(Integer, comment="年龄")
    work_year = Column(Integer, comment="工作年限")
    edu_level = Column(String(50), comment="学历")
    major = Column(String(200), comment="专业")
    phone = Column(String(20), comment="联系电话")
    email = Column(String(200), comment="邮箱")
    
    # 匹配信息
    job_description_id = Column(Integer, ForeignKey("job_descriptions.id"))
    match_score = Column(Float, comment="匹配度评分")
    match_result = Column(Text, comment="匹配结果详情")
    screening_status = Column(String(20), comment="筛选状态：pending/passed/failed")
    
    # 原始文档信息
    original_filename = Column(String(500), comment="原始文件名")
    file_path = Column(String(1000), comment="文件路径")
    raw_content = Column(Text, comment="原始内容")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    job_description = relationship("JobDescription", back_populates="resumes")
    interviews = relationship("Interview", back_populates="resume")

class Interview(Base):
    """面试表"""
    __tablename__ = "interviews"
    
    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes.id"), nullable=False)
    position_name = Column(String(100), comment="岗位名称")
    interview_time = Column(DateTime, comment="面试时间")
    interview_location = Column(String(200), comment="面试地点", default="会议室1")
    interviewer = Column(String(100), comment="面试官", default="张三")
    status = Column(String(20), comment="状态：scheduled/completed/cancelled")
    result = Column(String(20), comment="面试结果：pending/passed/failed")
    notes = Column(Text, comment="面试备注")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    resume = relationship("Resume", back_populates="interviews")
