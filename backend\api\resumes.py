from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os

from database import get_db
from models import Resume, JobDescription, Position
from services.document_service import DocumentService
from services.ai_service import AIService

router = APIRouter()
ai_service = AIService()

@router.get("/", response_model=List[dict])
async def get_resumes(db: Session = Depends(get_db)):
    """获取所有简历信息"""
    resumes = db.query(Resume).join(JobDescription, isouter=True).join(Position, isouter=True).all()
    return [
        {
            "id": resume.id,
            "name": resume.name,
            "gender": resume.gender,
            "age": resume.age,
            "work_year": resume.work_year,
            "edu_level": resume.edu_level,
            "major": resume.major,
            "phone": resume.phone,
            "email": resume.email,
            "job_description_id": resume.job_description_id,
            "position_name": resume.job_description.position.name if resume.job_description else None,
            "match_score": resume.match_score,
            "screening_status": resume.screening_status,
            "created_at": resume.created_at
        }
        for resume in resumes
    ]

@router.post("/upload")
async def upload_resume(
    job_description_id: int = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传简历文档"""
    if not file.filename.endswith('.docx'):
        raise HTTPException(status_code=400, detail="请上传Word文档(.docx)")
    
    # 验证JD是否存在
    jd = db.query(JobDescription).filter(JobDescription.id == job_description_id).first()
    if not jd:
        raise HTTPException(status_code=404, detail="JD不存在")
    
    try:
        # 保存文件
        file_path = await DocumentService.save_upload_file(file)
        
        # 读取文档内容
        doc_content = await DocumentService.read_docx_content(file_path)
        
        # 创建简历记录
        resume = Resume(
            job_description_id=job_description_id,
            original_filename=file.filename,
            file_path=file_path,
            raw_content=doc_content["raw_text"],
            screening_status="pending"
        )
        
        db.add(resume)
        db.commit()
        db.refresh(resume)
        
        return {
            "id": resume.id,
            "message": "简历上传成功",
            "filename": file.filename
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.post("/{resume_id}/ai-extract")
async def ai_extract_resume_info(resume_id: int, db: Session = Depends(get_db)):
    """AI提取简历信息"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    if not resume.raw_content:
        raise HTTPException(status_code=400, detail="简历内容为空")
    
    try:
        # 获取JD要求信息
        jd_requirements = {}
        if resume.job_description:
            jd_requirements = {
                "require_gender": resume.job_description.require_gender or "",
                "require_age": resume.job_description.require_age or "",
                "require_work_year": resume.job_description.require_work_year or "",
                "require_edu_level": resume.job_description.require_edu_level or "",
                "require_school": resume.job_description.require_school or "",
                "require_major": resume.job_description.require_major or ""
            }
        
        # 调用AI服务提取信息
        ai_result = await ai_service.match_resume(resume.raw_content, jd_requirements)
        
        if "error" in ai_result:
            raise HTTPException(status_code=500, detail=f"AI提取失败: {ai_result['error']}")
        
        # 更新简历信息
        if ai_result.get("resume_or_not"):
            resume.name = ai_result.get("name", "")
            resume.gender = ai_result.get("gender", "")
            resume.age = ai_result.get("age")
            resume.work_year = ai_result.get("work_year")
            resume.edu_level = ai_result.get("edu_level", "")
            resume.major = ai_result.get("major", "")
            
            db.commit()
            db.refresh(resume)
            
            return {
                "message": "AI提取成功",
                "extracted_info": {
                    "name": resume.name,
                    "gender": resume.gender,
                    "age": resume.age,
                    "work_year": resume.work_year,
                    "edu_level": resume.edu_level,
                    "major": resume.major
                }
            }
        else:
            raise HTTPException(status_code=400, detail="文档内容不是有效的简历")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI提取失败: {str(e)}")

@router.post("/{resume_id}/screening")
async def screen_resume(resume_id: int, db: Session = Depends(get_db)):
    """AI筛选简历"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    if not resume.job_description:
        raise HTTPException(status_code=400, detail="简历未关联JD")
    
    try:
        # 获取JD要求信息
        jd_requirements = {
            "require_gender": resume.job_description.require_gender or "",
            "require_age": resume.job_description.require_age or "",
            "require_work_year": resume.job_description.require_work_year or "",
            "require_edu_level": resume.job_description.require_edu_level or "",
            "require_school": resume.job_description.require_school or "",
            "require_major": resume.job_description.require_major or ""
        }
        
        # 调用AI服务进行匹配
        ai_result = await ai_service.match_resume(resume.raw_content, jd_requirements)
        
        if "error" in ai_result:
            raise HTTPException(status_code=500, detail=f"AI筛选失败: {ai_result['error']}")
        
        # 更新筛选结果
        conclusion = ai_result.get("conclusion", False)
        resume.screening_status = "passed" if conclusion else "failed"
        resume.match_result = ai_result.get("reason", "")
        
        # 计算匹配度评分（简单示例）
        if conclusion:
            resume.match_score = 85.0  # 通过的给85分
        else:
            resume.match_score = 45.0  # 不通过的给45分
        
        db.commit()
        db.refresh(resume)
        
        return {
            "message": "筛选完成",
            "screening_status": resume.screening_status,
            "match_score": resume.match_score,
            "match_result": resume.match_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"筛选失败: {str(e)}")

@router.get("/{resume_id}/match-details")
async def get_match_details(resume_id: int, db: Session = Depends(get_db)):
    """获取简历匹配详情"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    if not resume.job_description:
        raise HTTPException(status_code=400, detail="简历未关联JD")
    
    jd = resume.job_description
    
    return {
        "jd_requirements": {
            "require_gender": jd.require_gender,
            "require_age": jd.require_age,
            "require_work_year": jd.require_work_year,
            "require_edu_level": jd.require_edu_level,
            "require_school": jd.require_school,
            "require_major": jd.require_major
        },
        "match_result": {
            "match_score": resume.match_score,
            "screening_status": resume.screening_status,
            "match_details": resume.match_result
        }
    }

@router.put("/{resume_id}")
async def update_resume(
    resume_id: int,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    age: Optional[int] = None,
    work_year: Optional[int] = None,
    edu_level: Optional[str] = None,
    major: Optional[str] = None,
    phone: Optional[str] = None,
    email: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新简历信息"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    # 更新字段
    if name is not None:
        resume.name = name
    if gender is not None:
        resume.gender = gender
    if age is not None:
        resume.age = age
    if work_year is not None:
        resume.work_year = work_year
    if edu_level is not None:
        resume.edu_level = edu_level
    if major is not None:
        resume.major = major
    if phone is not None:
        resume.phone = phone
    if email is not None:
        resume.email = email
    
    db.commit()
    db.refresh(resume)
    
    return {"message": "简历更新成功"}

@router.delete("/{resume_id}")
async def delete_resume(resume_id: int, db: Session = Depends(get_db)):
    """删除简历"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    # 删除关联文件
    if resume.file_path and os.path.exists(resume.file_path):
        os.remove(resume.file_path)
    
    db.delete(resume)
    db.commit()
    
    return {"message": "简历删除成功"}
