import httpx
import json
import logging
from typing import Dict, Optional
import asyncio

logger = logging.getLogger(__name__)

class AIService:
    """AI服务集成"""
    
    def __init__(self):
        self.api_key = "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56"
        self.base_url = "https://api.ecovai.cn"
        self.model = "DeepSeek-V3-0324"
        self.timeout = 30
        self.max_retries = 3
    
    async def _make_request(self, messages: list, temperature: float = 0.1) -> Optional[str]:
        """发送AI请求"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 2000
        }
        
        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        f"{self.base_url}/v1/chat/completions",
                        headers=headers,
                        json=payload
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.error(f"AI请求失败，状态码: {response.status_code}, 响应: {response.text}")
                        
            except Exception as e:
                logger.error(f"AI请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    
        return None
    
    async def extract_jd_info(self, content: str) -> Dict:
        """从JD内容中提取信息"""
        prompt = """# 角色：JD信息提取助手

## 角色描述：
从招聘JD（Job Description）中提取岗位的任职条件，包括性别要求、年龄要求、工作年限、学历、毕业院校及专业要求，并进行规范化输出。

## 技能：
### 技能1：JD判断
根据输入内容判断是否为岗位描述（JD）。判断标准包括但不限于：是否包含岗位职责、任职要求、招聘条件等。如果内容明显不是JD（如个人简历、公司简介、随意文本），则判定为非JD。

### 技能2：信息提取
- 提取岗位性别要求（例如："女"、"不限"）。  
- 提取年龄要求（例如："30岁以下"、"35-45岁"）。  
- 提取核心专业经验要求的工作年限（例如："2年以上财务经验"）。  
- 提取学历要求（例如："本科及以上"）。  
- 提取毕业学校要求（例如："985/211高校优先"）。  
- 提取专业要求：如写明"相关专业"，则结合岗位类别转换为具体专业领域；如无法判断，保持"相关专业"。  

#### 注意：
- 当某项要求在JD中未出现，输出值设为空。  
- 当要求为"相关专业"，需结合岗位职责判断所属领域并转换。  
- 对于管理岗位，工作年限需区分专业经验和管理经验，优先提取核心专业经验。  

### 专业转换规则：
- 财务类岗位：财务管理、会计学、经济学、工商管理、金融学  
- 技术类岗位：计算机科学与技术、软件工程、信息技术、数字媒体技术  
- 人力资源类岗位：人力资源管理、工商管理、心理学、劳动关系  
- 市场营销类岗位：市场营销、工商管理、广告学、传播学、经济学  
- 若岗位类别不在上述范围 → 保持"相关专业"原文，不做转换  

## 输出要求：
请严格按照以下JSON格式输出，不要包含额外解释：
{
  "JD_or_not": true/false,
  "require_gender": "性别要求",
  "require_age": "年龄要求", 
  "require_work_year": "工作年限要求",
  "require_edu_level": "学历要求",
  "require_school": "学校要求",
  "require_major": "专业要求"
}

## 执行顺序：
1. 判断是否为JD  
   - 若不是JD → 输出 `JD_or_not=false`，其余字段全部 `"N/A"`  
   - 若是JD → 继续执行  
2. 从JD中逐项提取信息  
3. 若专业要求为"相关专业"，则根据岗位类别进行转换  
4. 按照输出要求统一格式输出，不要包含额外解释或自然语言描述  

请分析以下内容：
"""
        
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": content}
        ]
        
        response = await self._make_request(messages)
        
        if response:
            try:
                # 尝试解析JSON响应
                # 有时AI会返回包含```json的格式，需要清理
                clean_response = response.strip()
                if clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]
                
                result = json.loads(clean_response.strip())
                return result
            except json.JSONDecodeError as e:
                logger.error(f"解析AI响应JSON失败: {e}, 响应内容: {response}")
                return {"error": "AI响应格式错误"}
        
        return {"error": "AI请求失败"}
    
    async def match_resume(self, resume_content: str, jd_requirements: Dict) -> Dict:
        """简历匹配分析"""
        prompt = f"""# 角色：简历匹配判断助手

## 角色描述：
根据简历内容提取候选人基本信息，并与岗位标准进行比对。采用合理且宽松的匹配逻辑，用于初步筛选，确保大部分符合基本条件的简历能够通过，不做最终淘汰决策。

## 技能：
### 技能1：简历判断
判断输入内容是否为简历。标准包括但不限于：是否包含姓名、教育经历、工作经历等基本要素。

### 技能2：信息提取与匹配判断
- 性别：对比岗位要求 `{jd_requirements.get('require_gender', '')}` 
- 年龄：对比岗位要求 `{jd_requirements.get('require_age', '')}`（若缺失，默认匹配）  
- 工作年数：对比岗位要求 `{jd_requirements.get('require_work_year', '')}`，允许 ±0.5 年浮动（四舍五入为整数）  
- 学历：对比岗位要求 `{jd_requirements.get('require_edu_level', '')}`，学历等级顺序为：中专 < 大专 < 本科 < 硕士 < 博士；同等或更高学历视为匹配  
- 专业：对比岗位要求 `{jd_requirements.get('require_major', '')}`，采用宽松匹配（相关专业视为匹配）  

#### 匹配规则：
- **专业匹配**：模糊/相关专业均视为匹配（如工商管理 ≈ 财务管理 ≈ 市场营销）  
- **工作年数**：取整（2.4年算2年，2.6年算3年），允许 ±0.5 年浮动  
- **学历**：高于或等于要求则匹配  
- **空值处理**：当岗位要求为空时，直接判定为匹配  

### 技能3：综合判断
- 仅在明显不符合核心要求（如学历过低、经验不足、专业差异显著）时，才判定为不匹配  
- 给出客观简洁的结论，并列出不匹配项  

## 输出要求：
请严格按照以下JSON格式输出：
{{
  "resume_or_not": true/false,
  "conclusion": true/false,
  "reason": "匹配或不匹配的原因",
  "name": "姓名",
  "gender": "性别",
  "age": 年龄数字,
  "work_year": 工作年数整数,
  "edu_level": "最高学历",
  "major": "最高学历对应的专业"
}}

请分析以下简历内容：
"""
        
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": resume_content}
        ]
        
        response = await self._make_request(messages)
        
        if response:
            try:
                clean_response = response.strip()
                if clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]
                
                result = json.loads(clean_response.strip())
                return result
            except json.JSONDecodeError as e:
                logger.error(f"解析AI响应JSON失败: {e}, 响应内容: {response}")
                return {"error": "AI响应格式错误"}
        
        return {"error": "AI请求失败"}
