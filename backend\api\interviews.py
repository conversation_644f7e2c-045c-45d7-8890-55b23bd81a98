from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from database import get_db
from models import Interview, Resume, JobDescription, Position

router = APIRouter()

@router.get("/", response_model=List[dict])
async def get_interviews(db: Session = Depends(get_db)):
    """获取所有面试信息"""
    interviews = db.query(Interview).join(Resume).join(JobDescription).join(Position).all()
    return [
        {
            "id": interview.id,
            "candidate_name": interview.resume.name,
            "position_name": interview.position_name,
            "interview_time": interview.interview_time,
            "interview_location": interview.interview_location,
            "interviewer": interview.interviewer,
            "status": interview.status,
            "result": interview.result,
            "notes": interview.notes,
            "created_at": interview.created_at
        }
        for interview in interviews
    ]

@router.post("/")
async def create_interview_from_passed_resume(
    resume_id: int,
    db: Session = Depends(get_db)
):
    """从通过筛选的简历创建面试记录"""
    resume = db.query(Resume).filter(Resume.id == resume_id).first()
    if not resume:
        raise HTTPException(status_code=404, detail="简历不存在")
    
    if resume.screening_status != "passed":
        raise HTTPException(status_code=400, detail="只有通过筛选的简历才能安排面试")
    
    # 检查是否已经有面试记录
    existing_interview = db.query(Interview).filter(Interview.resume_id == resume_id).first()
    if existing_interview:
        raise HTTPException(status_code=400, detail="该候选人已有面试记录")
    
    # 获取岗位名称
    position_name = ""
    if resume.job_description and resume.job_description.position:
        position_name = resume.job_description.position.name
    
    interview = Interview(
        resume_id=resume_id,
        position_name=position_name,
        interview_location="会议室1",  # 默认值
        interviewer="张三",  # 默认值
        status="scheduled",
        result="pending"
    )
    
    db.add(interview)
    db.commit()
    db.refresh(interview)
    
    return {
        "id": interview.id,
        "message": "面试记录创建成功",
        "candidate_name": resume.name,
        "position_name": position_name
    }

@router.put("/{interview_id}/schedule")
async def schedule_interview(
    interview_id: int,
    interview_time: datetime,
    interview_location: Optional[str] = "会议室1",
    db: Session = Depends(get_db)
):
    """安排面试时间和地点"""
    interview = db.query(Interview).filter(Interview.id == interview_id).first()
    if not interview:
        raise HTTPException(status_code=404, detail="面试记录不存在")
    
    interview.interview_time = interview_time
    interview.interview_location = interview_location
    interview.status = "scheduled"
    
    db.commit()
    db.refresh(interview)
    
    return {
        "message": "面试安排成功",
        "interview_time": interview.interview_time,
        "interview_location": interview.interview_location
    }

@router.put("/{interview_id}/interviewer")
async def assign_interviewer(
    interview_id: int,
    interviewer: str = "张三",
    db: Session = Depends(get_db)
):
    """指定面试官"""
    interview = db.query(Interview).filter(Interview.id == interview_id).first()
    if not interview:
        raise HTTPException(status_code=404, detail="面试记录不存在")
    
    interview.interviewer = interviewer
    
    db.commit()
    db.refresh(interview)
    
    return {
        "message": "面试官指定成功",
        "interviewer": interview.interviewer
    }

@router.put("/{interview_id}/result")
async def update_interview_result(
    interview_id: int,
    result: str,  # passed/failed
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新面试结果"""
    if result not in ["passed", "failed"]:
        raise HTTPException(status_code=400, detail="面试结果只能是 passed 或 failed")
    
    interview = db.query(Interview).filter(Interview.id == interview_id).first()
    if not interview:
        raise HTTPException(status_code=404, detail="面试记录不存在")
    
    interview.result = result
    interview.status = "completed"
    if notes:
        interview.notes = notes
    
    db.commit()
    db.refresh(interview)
    
    return {
        "message": "面试结果更新成功",
        "result": interview.result,
        "status": interview.status
    }

@router.put("/{interview_id}")
async def update_interview(
    interview_id: int,
    interview_time: Optional[datetime] = None,
    interview_location: Optional[str] = None,
    interviewer: Optional[str] = None,
    status: Optional[str] = None,
    result: Optional[str] = None,
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新面试信息"""
    interview = db.query(Interview).filter(Interview.id == interview_id).first()
    if not interview:
        raise HTTPException(status_code=404, detail="面试记录不存在")
    
    # 更新字段
    if interview_time is not None:
        interview.interview_time = interview_time
    if interview_location is not None:
        interview.interview_location = interview_location
    if interviewer is not None:
        interview.interviewer = interviewer
    if status is not None:
        if status not in ["scheduled", "completed", "cancelled"]:
            raise HTTPException(status_code=400, detail="状态值无效")
        interview.status = status
    if result is not None:
        if result not in ["pending", "passed", "failed"]:
            raise HTTPException(status_code=400, detail="结果值无效")
        interview.result = result
    if notes is not None:
        interview.notes = notes
    
    db.commit()
    db.refresh(interview)
    
    return {"message": "面试信息更新成功"}

@router.delete("/{interview_id}")
async def delete_interview(interview_id: int, db: Session = Depends(get_db)):
    """删除面试记录"""
    interview = db.query(Interview).filter(Interview.id == interview_id).first()
    if not interview:
        raise HTTPException(status_code=404, detail="面试记录不存在")
    
    db.delete(interview)
    db.commit()
    
    return {"message": "面试记录删除成功"}

@router.get("/candidates")
async def get_interview_candidates(db: Session = Depends(get_db)):
    """获取可安排面试的候选人（筛选通过的简历）"""
    passed_resumes = db.query(Resume).filter(Resume.screening_status == "passed").all()
    
    candidates = []
    for resume in passed_resumes:
        # 检查是否已有面试记录
        existing_interview = db.query(Interview).filter(Interview.resume_id == resume.id).first()
        if not existing_interview:
            position_name = ""
            if resume.job_description and resume.job_description.position:
                position_name = resume.job_description.position.name
            
            candidates.append({
                "resume_id": resume.id,
                "name": resume.name,
                "position_name": position_name,
                "match_score": resume.match_score
            })
    
    return candidates
