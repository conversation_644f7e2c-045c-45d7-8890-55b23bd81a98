from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from datetime import datetime, timedelta

from database import get_db
from models import Position, JobDescription, Resume, Interview

router = APIRouter()

@router.get("/overview")
async def get_overview_statistics(db: Session = Depends(get_db)):
    """获取总览统计数据"""
    # 基础统计
    total_positions = db.query(Position).count()
    total_jds = db.query(JobDescription).count()
    total_resumes = db.query(Resume).count()
    total_interviews = db.query(Interview).count()
    
    # 简历筛选统计
    passed_resumes = db.query(Resume).filter(Resume.screening_status == "passed").count()
    failed_resumes = db.query(Resume).filter(Resume.screening_status == "failed").count()
    pending_resumes = db.query(Resume).filter(Resume.screening_status == "pending").count()
    
    # 面试统计
    scheduled_interviews = db.query(Interview).filter(Interview.status == "scheduled").count()
    completed_interviews = db.query(Interview).filter(Interview.status == "completed").count()
    passed_interviews = db.query(Interview).filter(Interview.result == "passed").count()
    
    return {
        "basic_stats": {
            "total_positions": total_positions,
            "total_jds": total_jds,
            "total_resumes": total_resumes,
            "total_interviews": total_interviews
        },
        "resume_screening": {
            "total": total_resumes,
            "passed": passed_resumes,
            "failed": failed_resumes,
            "pending": pending_resumes,
            "pass_rate": round(passed_resumes / total_resumes * 100, 2) if total_resumes > 0 else 0
        },
        "interview_stats": {
            "total": total_interviews,
            "scheduled": scheduled_interviews,
            "completed": completed_interviews,
            "passed": passed_interviews,
            "success_rate": round(passed_interviews / completed_interviews * 100, 2) if completed_interviews > 0 else 0
        }
    }

@router.get("/position-analysis")
async def get_position_analysis(db: Session = Depends(get_db)):
    """岗位发布和投递分析"""
    # 按岗位大类统计JD数量
    jd_by_category = db.query(
        Position.category,
        func.count(JobDescription.id).label('jd_count')
    ).join(JobDescription).group_by(Position.category).all()
    
    # 按岗位统计简历数量
    resume_by_position = db.query(
        Position.name,
        Position.category,
        func.count(Resume.id).label('resume_count')
    ).join(JobDescription).join(Resume).group_by(Position.id, Position.name, Position.category).all()
    
    # 按岗位统计通过率
    position_pass_rate = db.query(
        Position.name,
        func.count(Resume.id).label('total_resumes'),
        func.sum(func.case([(Resume.screening_status == 'passed', 1)], else_=0)).label('passed_resumes')
    ).join(JobDescription).join(Resume).group_by(Position.id, Position.name).all()
    
    return {
        "jd_by_category": [
            {"category": item.category, "jd_count": item.jd_count}
            for item in jd_by_category
        ],
        "resume_by_position": [
            {
                "position_name": item.name,
                "category": item.category,
                "resume_count": item.resume_count
            }
            for item in resume_by_position
        ],
        "position_pass_rate": [
            {
                "position_name": item.name,
                "total_resumes": item.total_resumes,
                "passed_resumes": item.passed_resumes or 0,
                "pass_rate": round((item.passed_resumes or 0) / item.total_resumes * 100, 2) if item.total_resumes > 0 else 0
            }
            for item in position_pass_rate
        ]
    }

@router.get("/time-series")
async def get_time_series_data(days: int = 30, db: Session = Depends(get_db)):
    """时间序列数据分析"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # 按日期统计简历投递数量
    daily_resumes = db.query(
        func.date(Resume.created_at).label('date'),
        func.count(Resume.id).label('count')
    ).filter(
        Resume.created_at >= start_date
    ).group_by(func.date(Resume.created_at)).all()
    
    # 按日期统计面试安排数量
    daily_interviews = db.query(
        func.date(Interview.created_at).label('date'),
        func.count(Interview.id).label('count')
    ).filter(
        Interview.created_at >= start_date
    ).group_by(func.date(Interview.created_at)).all()
    
    return {
        "daily_resumes": [
            {"date": str(item.date), "count": item.count}
            for item in daily_resumes
        ],
        "daily_interviews": [
            {"date": str(item.date), "count": item.count}
            for item in daily_interviews
        ]
    }

@router.get("/ai-performance")
async def get_ai_performance_stats(db: Session = Depends(get_db)):
    """AI筛选性能统计"""
    # 匹配度分布
    score_ranges = [
        (0, 20, "0-20"),
        (20, 40, "20-40"),
        (40, 60, "40-60"),
        (60, 80, "60-80"),
        (80, 100, "80-100")
    ]
    
    score_distribution = []
    for min_score, max_score, label in score_ranges:
        count = db.query(Resume).filter(
            and_(
                Resume.match_score >= min_score,
                Resume.match_score < max_score,
                Resume.match_score.isnot(None)
            )
        ).count()
        score_distribution.append({
            "range": label,
            "count": count
        })
    
    # 平均匹配度
    avg_score = db.query(func.avg(Resume.match_score)).filter(
        Resume.match_score.isnot(None)
    ).scalar()
    
    # AI提取成功率（有姓名的简历认为提取成功）
    total_processed = db.query(Resume).filter(Resume.raw_content.isnot(None)).count()
    successful_extractions = db.query(Resume).filter(
        and_(Resume.raw_content.isnot(None), Resume.name.isnot(None), Resume.name != "")
    ).count()
    
    return {
        "score_distribution": score_distribution,
        "average_score": round(avg_score, 2) if avg_score else 0,
        "extraction_stats": {
            "total_processed": total_processed,
            "successful_extractions": successful_extractions,
            "success_rate": round(successful_extractions / total_processed * 100, 2) if total_processed > 0 else 0
        }
    }

@router.get("/recruitment-funnel")
async def get_recruitment_funnel(db: Session = Depends(get_db)):
    """招聘漏斗分析"""
    # 各阶段数量统计
    total_resumes = db.query(Resume).count()
    screened_resumes = db.query(Resume).filter(Resume.screening_status.in_(["passed", "failed"])).count()
    passed_resumes = db.query(Resume).filter(Resume.screening_status == "passed").count()
    interviewed_candidates = db.query(Interview).count()
    hired_candidates = db.query(Interview).filter(Interview.result == "passed").count()
    
    funnel_data = [
        {"stage": "简历投递", "count": total_resumes, "percentage": 100},
        {"stage": "AI筛选", "count": screened_resumes, "percentage": round(screened_resumes / total_resumes * 100, 2) if total_resumes > 0 else 0},
        {"stage": "筛选通过", "count": passed_resumes, "percentage": round(passed_resumes / total_resumes * 100, 2) if total_resumes > 0 else 0},
        {"stage": "面试安排", "count": interviewed_candidates, "percentage": round(interviewed_candidates / total_resumes * 100, 2) if total_resumes > 0 else 0},
        {"stage": "面试通过", "count": hired_candidates, "percentage": round(hired_candidates / total_resumes * 100, 2) if total_resumes > 0 else 0}
    ]
    
    return {
        "funnel_data": funnel_data,
        "conversion_rates": {
            "screening_rate": round(screened_resumes / total_resumes * 100, 2) if total_resumes > 0 else 0,
            "pass_rate": round(passed_resumes / screened_resumes * 100, 2) if screened_resumes > 0 else 0,
            "interview_rate": round(interviewed_candidates / passed_resumes * 100, 2) if passed_resumes > 0 else 0,
            "hire_rate": round(hired_candidates / interviewed_candidates * 100, 2) if interviewed_candidates > 0 else 0
        }
    }
