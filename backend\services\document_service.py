import os
import pandas as pd
from docx import Document
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    """文档解析服务"""
    
    @staticmethod
    async def read_excel_positions(file_path: str) -> List[Dict]:
        """读取Excel岗位信息"""
        try:
            df = pd.read_excel(file_path)
            positions = []
            for _, row in df.iterrows():
                positions.append({
                    "category": row["岗位大类"],
                    "name": row["岗位名称"]
                })
            return positions
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    @staticmethod
    async def read_docx_content(file_path: str) -> Dict:
        """读取Word文档内容"""
        try:
            doc = Document(file_path)
            content = {
                "paragraphs": [],
                "tables": [],
                "raw_text": ""
            }
            
            # 读取段落
            for para in doc.paragraphs:
                if para.text.strip():
                    content["paragraphs"].append(para.text.strip())
            
            # 读取表格
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(row_data)
                content["tables"].append(table_data)
            
            # 合并所有文本内容
            all_text = []
            all_text.extend(content["paragraphs"])
            
            # 将表格内容也加入文本
            for table in content["tables"]:
                for row in table:
                    all_text.extend([cell for cell in row if cell])
            
            content["raw_text"] = "\n".join(all_text)
            
            return content
            
        except Exception as e:
            logger.error(f"读取Word文档失败: {e}")
            raise Exception(f"读取Word文档失败: {str(e)}")
    
    @staticmethod
    async def save_upload_file(upload_file, upload_dir: str = "uploads") -> str:
        """保存上传的文件"""
        try:
            # 确保上传目录存在
            os.makedirs(upload_dir, exist_ok=True)
            
            # 生成文件路径
            file_path = os.path.join(upload_dir, upload_file.filename)
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await upload_file.read()
                buffer.write(content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise Exception(f"保存文件失败: {str(e)}")
    
    @staticmethod
    def extract_table_content_as_text(tables: List[List[List[str]]]) -> str:
        """将表格内容转换为文本格式，便于AI处理"""
        text_content = []
        
        for i, table in enumerate(tables):
            text_content.append(f"表格{i+1}:")
            for row in table:
                # 过滤空单元格
                non_empty_cells = [cell for cell in row if cell.strip()]
                if non_empty_cells:
                    text_content.append(" | ".join(non_empty_cells))
            text_content.append("")  # 表格间空行
        
        return "\n".join(text_content)
