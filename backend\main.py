from fastapi import FastAPI, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
import uvicorn
import os

from database import get_db, engine, Base
from models import *
from services.document_service import DocumentService
from services.ai_service import AIService
from api import positions, jds, resumes, interviews, statistics

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="简历管理系统",
    description="基于AI的智能简历管理和筛选系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
if not os.path.exists("uploads"):
    os.makedirs("uploads")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 注册路由
app.include_router(positions.router, prefix="/api/positions", tags=["岗位管理"])
app.include_router(jds.router, prefix="/api/jds", tags=["JD管理"])
app.include_router(resumes.router, prefix="/api/resumes", tags=["简历管理"])
app.include_router(interviews.router, prefix="/api/interviews", tags=["面试管理"])
app.include_router(statistics.router, prefix="/api/statistics", tags=["统计报表"])

@app.get("/")
async def root():
    return {"message": "简历管理系统API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
