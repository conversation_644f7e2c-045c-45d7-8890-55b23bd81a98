from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os

from database import get_db
from models import JobDescription, Position
from services.document_service import DocumentService
from services.ai_service import AIService

router = APIRouter()
ai_service = AIService()

@router.get("/", response_model=List[dict])
async def get_jds(db: Session = Depends(get_db)):
    """获取所有JD信息"""
    jds = db.query(JobDescription).join(Position).all()
    return [
        {
            "id": jd.id,
            "source_type": jd.source_type,
            "position_category": jd.position.category,
            "position_name": jd.position.name,
            "company": jd.company,
            "department": jd.department,
            "location": jd.location,
            "require_gender": jd.require_gender,
            "require_age": jd.require_age,
            "require_work_year": jd.require_work_year,
            "require_edu_level": jd.require_edu_level,
            "require_school": jd.require_school,
            "require_major": jd.require_major,
            "created_at": jd.created_at
        }
        for jd in jds
    ]

@router.post("/manual")
async def create_manual_jd(
    position_id: int,
    company: str,
    department: str,
    location: str,
    require_gender: Optional[str] = None,
    require_age: Optional[str] = None,
    require_work_year: Optional[str] = None,
    require_edu_level: Optional[str] = None,
    require_school: Optional[str] = None,
    require_major: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """手工创建JD"""
    # 验证岗位是否存在
    position = db.query(Position).filter(Position.id == position_id).first()
    if not position:
        raise HTTPException(status_code=404, detail="岗位不存在")
    
    jd = JobDescription(
        source_type="manual",
        position_id=position_id,
        company=company,
        department=department,
        location=location,
        require_gender=require_gender,
        require_age=require_age,
        require_work_year=require_work_year,
        require_edu_level=require_edu_level,
        require_school=require_school,
        require_major=require_major
    )
    
    db.add(jd)
    db.commit()
    db.refresh(jd)
    
    return {
        "id": jd.id,
        "message": "JD创建成功"
    }

@router.post("/upload")
async def upload_jd_document(
    position_id: int = Form(...),
    company: str = Form(...),
    department: str = Form(...),
    location: str = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传JD文档"""
    if not file.filename.endswith('.docx'):
        raise HTTPException(status_code=400, detail="请上传Word文档(.docx)")
    
    # 验证岗位是否存在
    position = db.query(Position).filter(Position.id == position_id).first()
    if not position:
        raise HTTPException(status_code=404, detail="岗位不存在")
    
    try:
        # 保存文件
        file_path = await DocumentService.save_upload_file(file)
        
        # 读取文档内容
        doc_content = await DocumentService.read_docx_content(file_path)
        
        # 创建JD记录
        jd = JobDescription(
            source_type="upload",
            position_id=position_id,
            company=company,
            department=department,
            location=location,
            original_filename=file.filename,
            file_path=file_path,
            raw_content=doc_content["raw_text"]
        )
        
        db.add(jd)
        db.commit()
        db.refresh(jd)
        
        return {
            "id": jd.id,
            "message": "JD文档上传成功",
            "filename": file.filename
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.post("/{jd_id}/ai-extract")
async def ai_extract_jd_info(jd_id: int, db: Session = Depends(get_db)):
    """AI提取JD信息"""
    jd = db.query(JobDescription).filter(JobDescription.id == jd_id).first()
    if not jd:
        raise HTTPException(status_code=404, detail="JD不存在")
    
    if jd.source_type != "upload" or not jd.raw_content:
        raise HTTPException(status_code=400, detail="只能对上传的文档进行AI提取")
    
    try:
        # 调用AI服务提取信息
        ai_result = await ai_service.extract_jd_info(jd.raw_content)
        
        if "error" in ai_result:
            raise HTTPException(status_code=500, detail=f"AI提取失败: {ai_result['error']}")
        
        # 更新JD信息
        if ai_result.get("JD_or_not"):
            jd.require_gender = ai_result.get("require_gender", "")
            jd.require_age = ai_result.get("require_age", "")
            jd.require_work_year = ai_result.get("require_work_year", "")
            jd.require_edu_level = ai_result.get("require_edu_level", "")
            jd.require_school = ai_result.get("require_school", "")
            jd.require_major = ai_result.get("require_major", "")
            
            db.commit()
            db.refresh(jd)
            
            return {
                "message": "AI提取成功",
                "extracted_info": {
                    "require_gender": jd.require_gender,
                    "require_age": jd.require_age,
                    "require_work_year": jd.require_work_year,
                    "require_edu_level": jd.require_edu_level,
                    "require_school": jd.require_school,
                    "require_major": jd.require_major
                }
            }
        else:
            raise HTTPException(status_code=400, detail="文档内容不是有效的JD")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI提取失败: {str(e)}")

@router.put("/{jd_id}")
async def update_jd(
    jd_id: int,
    company: Optional[str] = None,
    department: Optional[str] = None,
    location: Optional[str] = None,
    require_gender: Optional[str] = None,
    require_age: Optional[str] = None,
    require_work_year: Optional[str] = None,
    require_edu_level: Optional[str] = None,
    require_school: Optional[str] = None,
    require_major: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新JD信息"""
    jd = db.query(JobDescription).filter(JobDescription.id == jd_id).first()
    if not jd:
        raise HTTPException(status_code=404, detail="JD不存在")
    
    # 更新字段
    if company is not None:
        jd.company = company
    if department is not None:
        jd.department = department
    if location is not None:
        jd.location = location
    if require_gender is not None:
        jd.require_gender = require_gender
    if require_age is not None:
        jd.require_age = require_age
    if require_work_year is not None:
        jd.require_work_year = require_work_year
    if require_edu_level is not None:
        jd.require_edu_level = require_edu_level
    if require_school is not None:
        jd.require_school = require_school
    if require_major is not None:
        jd.require_major = require_major
    
    db.commit()
    db.refresh(jd)
    
    return {"message": "JD更新成功"}

@router.delete("/{jd_id}")
async def delete_jd(jd_id: int, db: Session = Depends(get_db)):
    """删除JD"""
    jd = db.query(JobDescription).filter(JobDescription.id == jd_id).first()
    if not jd:
        raise HTTPException(status_code=404, detail="JD不存在")
    
    # 删除关联文件
    if jd.file_path and os.path.exists(jd.file_path):
        os.remove(jd.file_path)
    
    db.delete(jd)
    db.commit()
    
    return {"message": "JD删除成功"}
